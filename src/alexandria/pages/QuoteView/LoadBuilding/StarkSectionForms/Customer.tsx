import { FieldPath, UseFormReturn } from 'react-hook-form';

import { RHFTextInput } from 'components/input/RHFTextInput';
import { ReferenceNumberInput } from 'components/input/ReferenceNumberInput';
import { NormalizedLoad } from 'types/Load';

type LoadBuildingTextInputProps = React.ComponentPropsWithoutRef<
  typeof RHFTextInput
> & { name: FieldPath<NormalizedLoad> };

export const LoadBuildingTextInput = (props: LoadBuildingTextInputProps) => (
  <RHFTextInput {...props} />
);

export type CustomerSectionFormProps = {
  formMethods: UseFormReturn<NormalizedLoad>;
  originalSuggestionData?: NormalizedLoad;
  isCreateMode?: boolean;
};

export function CustomerSectionForm({
  formMethods,
  originalSuggestionData,
  isCreateMode = false,
}: CustomerSectionFormProps) {
  const {
    control,
    getValues,
  } = formMethods;

  return (
    <>
      <LoadBuildingTextInput
        name='customer.name'
        label='Name'
        readOnly={!isCreateMode}
        required={isCreateMode}
      />

      <ReferenceNumberInput
        name='customer.refNumber'
        control={control}
        label='Ref #/ BOL'
        placeholder='LD12345'
        load={originalSuggestionData || getValues()}
      />

      <RHFTextInput name={'poNums'} label='PO #' placeholder='123456' readOnly/>
    </>
  );
}
