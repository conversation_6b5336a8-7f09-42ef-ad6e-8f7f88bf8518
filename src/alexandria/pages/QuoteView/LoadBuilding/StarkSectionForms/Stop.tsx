import { useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';

import { RHFTextInput } from 'components/input/RHFTextInput';
import { LoadDateTimeInput, LoadSelectInput } from 'pages/LoadView/LoadInformation/Components';
import { NormalizedLoad } from 'types/Load';
import { datetimeFieldOptions } from 'utils/formValidators';
import { Flex } from 'components/layout';
import { LoadBuildingTextInput } from './Customer';

interface StopFormProps {
  formMethods: UseFormReturn<NormalizedLoad>;
  stopType: 'pickup' | 'consignee';
  load?: NormalizedLoad;
}

export function StopForm({
  formMethods,
  stopType,
  load,
}: StopFormProps) {
  const { watch, setValue } = formMethods;
  const prefix = stopType;

  // Watch appointment type to conditionally show fields
  const watchedApptType = watch(`${prefix}.apptType`);

  const currentLoad = watch(); // Get current form values

  useEffect(() => {
    if (watchedApptType === 'appointment') {
      setValue(`${prefix}.apptEndTime`, null);
    }
  }, [watchedApptType, setValue, prefix]);

  return (
    <>
      <RHFTextInput name={`${prefix}.city`} label='City' readOnly={true} />
      <Flex direction='row' className='gap-4'>
        <Flex grow='1'>
          <LoadBuildingTextInput
            name={`${prefix}.state`}
            label='State'
            readOnly
          />
        </Flex>
        <Flex grow='1'>
          <LoadBuildingTextInput
            name={`${prefix}.zipCode`}
            label='ZIP Code'
            readOnly
            placeholder='12345'
          />
        </Flex>
      </Flex>
      <RHFTextInput
        name={`${prefix}.refNumber`}
        label='Reference Number'
        readOnly={true}
      />

      <LoadSelectInput
        name={`${prefix}.apptType`}
        label='Appointment Type'
        options={[
          { value: 'appointment', label: 'Appointment' },
          { value: 'range', label: 'Range' },
        ]}
        placeholder='Select appointment type'
      />

      {watchedApptType && (
        <>
          <LoadDateTimeInput
            name={`${prefix}.apptStartTime`}
            label='Appointment Start Time'
            options={datetimeFieldOptions}
            useUTCWhenNoTimezone
            load={load}
            disablePastDates={false}
          />

          {watchedApptType === 'range' && (
            <LoadDateTimeInput
              name={`${prefix}.apptEndTime`}
              label='Appointment End Time'
              options={datetimeFieldOptions}
              useUTCWhenNoTimezone
              load={load}
              disablePastDates={false}
            />
          )}

          <RHFTextInput name={`${prefix}.apptNote`} label='Appointment Note' />
        </>
      )}

      { prefix === 'pickup' && (
        <>
          <LoadDateTimeInput
            name='carrier.pickupStart'
            label='Checked In'
            options={datetimeFieldOptions}
            load={currentLoad}
            readOnly
          />
          <LoadDateTimeInput
            name='carrier.pickupEnd'
            label='Checked Out'
            options={datetimeFieldOptions}
            load={currentLoad}
            readOnly
          />
      </>
      )}

      { prefix === 'consignee' && (
        <>
          <LoadDateTimeInput
            name='carrier.deliveryStart'
            label='Checked In'
            options={datetimeFieldOptions}
            load={currentLoad}
            readOnly
          />
          <LoadDateTimeInput
            name='carrier.deliveryEnd'
            label='Checked Out'
            options={datetimeFieldOptions}
            load={currentLoad}
            readOnly
          />
      </>
      )}
    </>
  );
}

export default StopForm;
