import { UseFormReturn } from 'react-hook-form';

import { InputValue } from 'components/input/RHFTextInput';
import {
  LoadDateTimeInput,
  LoadTextInput,
} from 'pages/LoadView/LoadInformation/Components';
import { NormalizedLoad } from 'types/Load';
import { datetimeFieldOptions } from 'utils/formValidators';
import { LoadBuildingTextInput } from './Customer';

export function CarrierForm({
  formMethods,
  isCreateMode = false
}: {
  formMethods: UseFormReturn<NormalizedLoad>;
  isCreateMode?: boolean;
}) {
  const {
    watch,
  } = formMethods;

  const currentLoad = watch(); // Get current form values

  return (
    <>
      <LoadBuildingTextInput
        name='carrier.externalTMSID'
        label='Name'
        readOnly={!isCreateMode}
        required={isCreateMode}
      />
      <LoadTextInput name='mode' label='Mode' readOnly/>
      <LoadTextInput name='carrier.dispatcher' label='Dispatcher' readOnly/>

      <LoadTextInput
        name='carrier.firstDriverName'
        label='First Driver Name'
        readOnly
      />
      <LoadTextInput
        name='carrier.firstDriverPhone'
        label='First Driver Phone'
        inputValue={InputValue.PHONE_NUMBER}
        readOnly
      />

      <LoadTextInput
        name='carrier.dispatchCity'
        label='Dispatch City'
        readOnly
      />
      <LoadTextInput
        name='carrier.dispatchState'
        label='Dispatch State'
        readOnly
      />
      <LoadTextInput
        name='carrier.truckNumber'
        label='Truck #'
        readOnly
      />
      <LoadTextInput
        name='carrier.trailerNumber'
        label='Trailer #'
        readOnly
      />

      {/* <LoadTextInput name='carrier.notes' label='Notes' /> */}

      <LoadTextInput
        name='carrier.rateConfirmationSent'
        label='Rate Confirmation Sent'
        readOnly
      />
      <LoadDateTimeInput
        name='carrier.confirmationSentTime'
        label='Rate Confirmation Sent Time'
        options={datetimeFieldOptions}
        load={currentLoad}
        readOnly
      />
      <LoadTextInput name='carrier.signedBy' label='Rate Confirmation Signed By' readOnly/>
    </>
  );
}
